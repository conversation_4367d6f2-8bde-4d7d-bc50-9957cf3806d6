<div tabindex="0" ><div ><div ><div ><section><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><p>An ES|QL (Elasticsearch query language) query consists of a series of commands, separated by pipe characters: <code  data-code-language="text">|</code>. Each query starts with a <strong>source command</strong>, which produces a table, typically with data from Elasticsearch.</p>
<p>A source command can be followed by one or more <strong>processing commands</strong>. Processing commands can change the output table of the previous command by adding, removing, and changing rows and columns.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">source-command
| processing-command1
| processing-command2
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>The result of a query is the table produced by the final processing command.
</p></div></section><section class="css-fn8403"><h2>Source commands</h2><p>A source command produces a table, typically with data from Elasticsearch. ES|QL supports the following source commands.</p><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>FROM</h3>
<p>The <code  data-code-language="text">FROM</code> source command returns a table with up to 10,000 documents from a data stream, index, or alias. Each row in the resulting table represents a document. Each column corresponds to a field, and can be accessed by the name of that field.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>You can use <a  href="https://www.elastic.co/guide/en/elasticsearch/reference/current/api-conventions.html#api-date-math-index-names" target="_blank" rel="noopener noreferrer">date math<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"  role="presentation" data-icon-type="popout" data-is-loaded="true" aria-hidden="true"><path d="M11 2h2.293L6.646 8.646l.708.708L14 2.707V5h1V1h-4v1Z"></path><path d="M3 2a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V7h-1v6H3V3h6V2H3Z"></path></svg><span >(external, opens in a new tab or window)</span></a> to refer to indices, aliases and data streams. This can be useful for time series data.</p>
<p>Use comma-separated lists or wildcards to query multiple data streams, indices, or aliases:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees-00001,employees-*
</code></pre><div ><div ><span ></span></div></div></div></div>
<h4>Metadata</h4>
<p>ES|QL can access the following metadata fields:</p>
<ul>
<li><code  data-code-language="text">_index</code>: the index to which the document belongs. The field is of the type <code  data-code-language="text">keyword</code>.</li>
<li><code  data-code-language="text">_id</code>: the source document's ID. The field is of the type <code  data-code-language="text">keyword</code>.</li>
<li><code  data-code-language="text">_version</code>: the source document's version. The field is of the type <code  data-code-language="text">long</code>.</li>
</ul>
<p>Use the <code  data-code-language="text">METADATA</code> directive to enable metadata fields:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM index METADATA _index, _id
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Metadata fields are only available if the source of the data is an index. Consequently, <code  data-code-language="text">FROM</code> is the only source commands that supports the <code  data-code-language="text">METADATA</code> directive.</p>
<p>Once enabled, the fields are then available to subsequent processing commands, just like the other index fields:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM ul_logs, apps METADATA _index, _version
| WHERE id IN (13, 14) AND _version == 1
| EVAL key = CONCAT(_index, "_", TO_STR(id))
| SORT id, _index
| KEEP id, _index, _version, key
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Also, similar to the index fields, once an aggregation is performed, a metadata field will no longer be accessible to subsequent commands, unless used as grouping field:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees METADATA _index, _id
| STATS max = MAX(emp_no) BY _index
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>ROW</h3>
<p>The <code  data-code-language="text">ROW</code> source command produces a row with one or more columns with values that you specify. This can be useful for testing.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = 1, b = "two", c = null
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Use square brackets to create multi-value columns:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = [2, 1]
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>ROW supports the use of functions:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = ROUND(1.23, 0)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>SHOW</h3>
<p>The <code  data-code-language="text">SHOW &lt;item&gt;</code> source command returns information about the deployment and its capabilities:</p>
<ul>
<li>Use <code  data-code-language="text">SHOW INFO</code> to return the deployment's version, build date and hash.</li>
<li>Use <code  data-code-language="text">SHOW FUNCTIONS</code> to return a list of all supported functions and a synopsis of each function.
</li>
</ul></div></article></section><section class="css-fn8403"><h2>Processing commands</h2><p>Processing commands change an input table by adding, removing, or changing rows and columns. ES|QL supports the following processing commands.</p><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>DISSECT</h3>
<p><code  data-code-language="text">DISSECT</code> enables you to extract structured data out of a string. <code  data-code-language="text">DISSECT</code> matches the string against a delimiter-based pattern, and extracts the specified keys as columns.</p>
<p>Refer to the <a  href="https://www.elastic.co/guide/en/elasticsearch/reference/current/dissect-processor.html" target="_blank" rel="noopener noreferrer">dissect processor documentation<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"  role="presentation" data-icon-type="popout" data-is-loaded="true" aria-hidden="true"><path d="M11 2h2.293L6.646 8.646l.708.708L14 2.707V5h1V1h-4v1Z"></path><path d="M3 2a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V7h-1v6H3V3h6V2H3Z"></path></svg><span >(external, opens in a new tab or window)</span></a> for the syntax of dissect patterns.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = "1953-01-23T12:15:00Z - some text - 127.0.0.1"
| DISSECT a "%{Y}-%{M}-%{D}T%{h}:%{m}:%{s}Z - %{msg} - %{ip}"
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>DROP</h3>
<p>Use <code  data-code-language="text">DROP</code> to remove columns from a table:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| DROP height
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Rather than specify each column by name, you can use wildcards to drop all columns with a name that matches a pattern:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| DROP height*
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>ENRICH</h3>
<p>You can use <code  data-code-language="text">ENRICH</code> to add data from your existing indices to incoming records. It’s similar to <a  href="https://www.elastic.co/guide/en/elasticsearch/reference/current/ingest-enriching-data.html" target="_blank" rel="noopener noreferrer">ingest enrich<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"  role="presentation" data-icon-type="popout" data-is-loaded="true" aria-hidden="true"><path d="M11 2h2.293L6.646 8.646l.708.708L14 2.707V5h1V1h-4v1Z"></path><path d="M3 2a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V7h-1v6H3V3h6V2H3Z"></path></svg><span >(external, opens in a new tab or window)</span></a>, but it works at query time.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW language_code = "1"
| ENRICH languages_policy
</code></pre><div ><div ><span ></span></div></div></div></div>
<p><code  data-code-language="text">ENRICH</code> requires an <a  href="https://www.elastic.co/guide/en/elasticsearch/reference/current/ingest-enriching-data.html#enrich-policy" target="_blank" rel="noopener noreferrer">enrich policy<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"  role="presentation" data-icon-type="popout" data-is-loaded="true" aria-hidden="true"><path d="M11 2h2.293L6.646 8.646l.708.708L14 2.707V5h1V1h-4v1Z"></path><path d="M3 2a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V7h-1v6H3V3h6V2H3Z"></path></svg><span >(external, opens in a new tab or window)</span></a> to be executed. The enrich policy defines a match field (a key field) and a set of enrich fields.</p>
<p><code  data-code-language="text">ENRICH</code> will look for records in the <a  href="https://www.elastic.co/guide/en/elasticsearch/reference/current/ingest-enriching-data.html#enrich-index" target="_blank" rel="noopener noreferrer">enrich index<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"  role="presentation" data-icon-type="popout" data-is-loaded="true" aria-hidden="true"><path d="M11 2h2.293L6.646 8.646l.708.708L14 2.707V5h1V1h-4v1Z"></path><path d="M3 2a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V7h-1v6H3V3h6V2H3Z"></path></svg><span >(external, opens in a new tab or window)</span></a> based on the match field value. The matching key in the input dataset can be defined using <code  data-code-language="text">ON &lt;field-name&gt;</code>; if it’s not specified, the match will be performed on a field with the same name as the match field defined in the enrich policy.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = "1"
| ENRICH languages_policy ON a
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>You can specify which attributes (between those defined as enrich fields in the policy) have to be added to the result, using <code  data-code-language="text">WITH &lt;field1&gt;, &lt;field2&gt;...</code> syntax.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = "1"
| ENRICH languages_policy ON a WITH language_name
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Attributes can also be renamed using <code  data-code-language="text">WITH new_name=&lt;field1&gt;</code></p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = "1"
| ENRICH languages_policy ON a WITH name = language_name
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>By default (if no <code  data-code-language="text">WITH</code> is defined), <code  data-code-language="text">ENRICH</code> will add all the enrich fields defined in the enrich policy to the result.</p>
<p>In case of name collisions, the newly created fields will override the existing fields.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>EVAL</h3>
<p><code  data-code-language="text">EVAL</code> enables you to add new columns:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, height
| EVAL height_feet = height * 3.281, height_cm = height * 100
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>If the specified column already exists, the existing column will be dropped, and the new column will be appended to the table:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, height
| EVAL height = height * 3.281
</code></pre><div ><div ><span ></span></div></div></div></div>
<h4>Functions</h4>
<p><code  data-code-language="text">EVAL</code> supports various functions for calculating values. Refer to Functions for more information.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>GROK</h3>
<p><code  data-code-language="text">GROK</code> enables you to extract structured data out of a string. <code  data-code-language="text">GROK</code> matches the string against patterns, based on regular expressions, and extracts the specified patterns as columns.</p>
<p>Refer to the <a  href="https://www.elastic.co/guide/en/elasticsearch/reference/current/grok-processor.html" target="_blank" rel="noopener noreferrer">grok processor documentation<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"  role="presentation" data-icon-type="popout" data-is-loaded="true" aria-hidden="true"><path d="M11 2h2.293L6.646 8.646l.708.708L14 2.707V5h1V1h-4v1Z"></path><path d="M3 2a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V7h-1v6H3V3h6V2H3Z"></path></svg><span >(external, opens in a new tab or window)</span></a> for the syntax of grok patterns.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = "12 15.5 15.6 true"
| GROK a "%{NUMBER:b:int} %{NUMBER:c:float} %{NUMBER:d:double} %{WORD:e:boolean}"
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>KEEP</h3>
<p>The <code  data-code-language="text">KEEP</code> command enables you to specify what columns are returned and the order in which they are returned.</p>
<p>To limit the columns that are returned, use a comma-separated list of column names. The columns are returned in the specified order:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, height
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Rather than specify each column by name, you can use wildcards to return all columns with a name that matches a pattern:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP h*
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>The asterisk wildcard (<code  data-code-language="text">*</code>) by itself translates to all columns that do not match the other arguments. This query will first return all columns with a name that starts with an h, followed by all other columns:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP h*, *
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>LIMIT</h3>
<p>The <code  data-code-language="text">LIMIT</code> processing command enables you to limit the number of rows:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| LIMIT 5
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><span ><span tabindex="0" >Technical Preview</span></span><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>LOOKUP JOIN</h3>
<p>You can use <code  data-code-language="text">LOOKUP JOIN</code> to add data from an existing index to incoming rows. While this is similar to <code  data-code-language="text">ENRICH</code>, it does not require an enrich policy to be executed beforehand. Additionally, if multiple matching documents are found in the lookup index, they will generate multiple output rows.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW language_code = 1
| LOOKUP JOIN languages ON language_code
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>An index that is used in <code  data-code-language="text">LOOKUP JOIN</code> needs to be in lookup mode. To create a lookup index, set the index mode to lookup.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">PUT languages
{
  "settings": {
    "index":{
      "mode":"lookup"
    }
  }
}
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>The join key field must have a compatible type and match the name of the field in the lookup index to find matching documents. You can use <code  data-code-language="text">RENAME</code> or <code  data-code-language="text">EVAL</code> to rename columns as needed.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| EVAL language_code = languages
| LOOKUP JOIN languages ON language_code
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>In case of name collisions, the fields from the lookup index will override the existing fields.
</p></div></article><article ><span ><span tabindex="0" >Technical Preview</span></span><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>MV_EXPAND</h3>
<p>The <code  data-code-language="text">MV_EXPAND</code> processing command expands multivalued fields into one row per value, duplicating other fields:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=[1,2,3], b="b", j=["a","b"]
| MV_EXPAND a
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>RENAME</h3>
<p>Use <code  data-code-language="text">RENAME</code> to rename a column using the following syntax:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">RENAME &lt;old-name&gt; AS &lt;new-name&gt;
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>For example:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, still_hired
| RENAME still_hired AS employed
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>If a column with the new name already exists, it will be replaced by the new column.</p>
<p>Multiple columns can be renamed with a single <code  data-code-language="text">RENAME</code> command:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name
| RENAME first_name AS fn, last_name AS ln
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>SORT</h3>
<p>Use the <code  data-code-language="text">SORT</code> command to sort rows on one or more fields:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, height
| SORT height
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>The default sort order is ascending. Set an explicit sort order using <code  data-code-language="text">ASC</code> or <code  data-code-language="text">DESC</code>:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, height
| SORT height DESC
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>If two rows have the same sort key, the original order will be preserved. You can provide additional sort expressions to act as tie breakers:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, height
| SORT height DESC, first_name ASC
</code></pre><div ><div ><span ></span></div></div></div></div>
<h4><code  data-code-language="text">null</code> values</h4>
<p>By default, <code  data-code-language="text">null</code> values are treated as being larger than any other value. With an ascending sort order, <code  data-code-language="text">null</code> values are sorted last, and with a descending sort order, <code  data-code-language="text">null</code> values are sorted first. You can change that by providing <code  data-code-language="text">NULLS FIRST</code> or <code  data-code-language="text">NULLS LAST</code>:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, height
| SORT first_name ASC NULLS FIRST
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>STATS ... BY</h3>
<p>Use <code  data-code-language="text">STATS ... BY</code> to group rows according to a common value and calculate one or more aggregated values over the grouped rows.</p>
<p><strong>Examples</strong>:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS count = COUNT(emp_no) BY languages
| SORT languages
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>If <code  data-code-language="text">BY</code> is omitted, the output table contains exactly one row with the aggregations applied over the entire dataset:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS avg_lang = AVG(languages)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>It's possible to calculate multiple values:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS avg_lang = AVG(languages), max_lang = MAX(languages)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>It's also possible to group by multiple values (only supported for long and keyword family fields):</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| EVAL hired = DATE_FORMAT(hire_date, "YYYY")
| STATS avg_salary = AVG(salary) BY hired, languages.long
| EVAL avg_salary = ROUND(avg_salary)
| SORT hired, languages.long
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Refer to <strong>Aggregation functions</strong> for a list of functions that can be used with <code  data-code-language="text">STATS ... BY</code>.</p>
<p>Both the aggregating functions and the grouping expressions accept other functions. This is useful for using <code  data-code-language="text">STATS...BY</code> on multivalue columns. For example, to calculate the average salary change, you can use <code  data-code-language="text">MV_AVG</code> to first average the multiple values per employee, and use the result with the <code  data-code-language="text">AVG</code> function:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS avg_salary_change = AVG(MV_AVG(salary_change))
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>An example of grouping by an expression is grouping employees on the first letter of their last name:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS my_count = COUNT() BY LEFT(last_name, 1)
| SORT `LEFT(last_name, 1)`
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Specifying the output column name is optional. If not specified, the new column name is equal to the expression. The following query returns a column named <code  data-code-language="text">AVG(salary)</code>:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS AVG(salary)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Because this name contains special characters, it needs to be quoted with backticks (`) when using it in subsequent commands:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS AVG(salary)
| EVAL avg_salary_rounded = ROUND(`AVG(salary)`)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p><strong>Note</strong>: <code  data-code-language="text">STATS</code> without any groups is much faster than adding a group.</p>
<p><strong>Note</strong>: Grouping on a single expression is currently much more optimized than grouping on many expressions.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>WHERE</h3>
<p>Use <code  data-code-language="text">WHERE</code> to produce a table that contains all the rows from the input table for which the provided condition evaluates to <code  data-code-language="text">true</code>:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, still_hired
| WHERE still_hired == true
</code></pre><div ><div ><span ></span></div></div></div></div>
<h4>Operators</h4>
<p>Refer to <strong>Operators</strong> for an overview of the supported operators.</p>
<h4>Functions</h4>
<p><code  data-code-language="text">WHERE</code> supports various functions for calculating values. Refer to <strong>Functions</strong> for more information.
</p></div></article></section><section class="css-fn8403"><h2>Functions</h2><p>Functions are supported by ROW, EVAL and WHERE.</p><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ABS</h3>
<p>  Returns the absolute value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW number = -1.0 
| EVAL abs_number = ABS(number)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ACOS</h3>
<p>  Returns the arccosine of <code  data-code-language="text">n</code> as an angle, expressed in radians.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=.9
| EVAL acos=ACOS(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ASIN</h3>
<p>  Returns the arcsine of the input
numeric expression as an angle, expressed in radians.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=.9
| EVAL asin=ASIN(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ATAN</h3>
<p>  Returns the arctangent of the input
numeric expression as an angle, expressed in radians.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=12.9
| EVAL atan=ATAN(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ATAN2</h3>
<p>  The angle between the positive x-axis and the ray from the
origin to the point (x , y) in the Cartesian plane, expressed in radians.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW y=12.9, x=.6
| EVAL atan2=ATAN2(y, x)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>BIT_LENGTH</h3>
<p>  Returns the bit length of a string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airports
| WHERE country == "India"
| KEEP city
| EVAL fn_length = LENGTH(city), fn_bit_length = BIT_LENGTH(city)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: All strings are in UTF-8, so a single character can use multiple bytes.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>BUCKET</h3>
<p>  Creates groups of values - buckets - out of a datetime or numeric input.
The size of the buckets can either be provided directly, or chosen based on a recommended count and values range.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE hire_date &gt;= "1985-01-01T00:00:00Z" AND hire_date &lt; "1986-01-01T00:00:00Z"
| STATS hire_date = MV_SORT(VALUES(hire_date)) BY month = BUCKET(hire_date, 20, "1985-01-01T00:00:00Z", "1986-01-01T00:00:00Z")
| SORT hire_date
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>BYTE_LENGTH</h3>
<p>  Returns the byte length of a string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airports
| WHERE country == "India"
| KEEP city
| EVAL fn_length = LENGTH(city), fn_byte_length = BYTE_LENGTH(city)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: All strings are in UTF-8, so a single character can use multiple bytes.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>CASE</h3>
<p>  Accepts pairs of conditions and values. The function returns the value that
belongs to the first condition that evaluates to <code  data-code-language="text">true</code>.</p>
<p>  If the number of arguments is odd, the last argument is the default value which
is returned when no condition matches. If the number of arguments is even, and
no condition matches, the function returns <code  data-code-language="text">null</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| EVAL type = CASE(
    languages &lt;= 1, "monolingual",
    languages &lt;= 2, "bilingual",
     "polyglot")
| KEEP emp_no, languages, type
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><span ><span tabindex="0" >Technical Preview</span></span><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>CATEGORIZE</h3>
<p>  Groups text messages into categories of similarly formatted text values.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM sample_data
| STATS count=COUNT() BY category=CATEGORIZE(message)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>CBRT</h3>
<p>  Returns the cube root of a number. The input can be any numeric value, the return value is always a double.
Cube roots of infinities are null.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW d = 1000.0
| EVAL c = cbrt(d)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>CEIL</h3>
<p>  Round a number up to the nearest integer.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=1.8
| EVAL a=CEIL(a)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: This is a noop for <code  data-code-language="text">long</code> (including unsigned) and <code  data-code-language="text">integer</code>. For <code  data-code-language="text">double</code> this picks the closest <code  data-code-language="text">double</code> value to the integer similar to Math.ceil.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>CIDR_MATCH</h3>
<p>  Returns true if the provided IP is contained in one of the provided CIDR blocks.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM hosts 
| WHERE CIDR_MATCH(ip1, "*********/32", "*********/32") 
| KEEP card, host, ip0, ip1
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>COALESCE</h3>
<p>  Returns the first of its arguments that is not null. If all arguments are null, it returns <code  data-code-language="text">null</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=null, b="b"
| EVAL COALESCE(a, b)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>CONCAT</h3>
<p>  Concatenates two or more strings.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name
| EVAL fullname = CONCAT(first_name, " ", last_name)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>COS</h3>
<p>  Returns the cosine of an angle.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=1.8 
| EVAL cos=COS(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>COSH</h3>
<p>  Returns the hyperbolic cosine of a number.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=1.8 
| EVAL cosh=COSH(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>DATE_DIFF</h3>
<p>  Subtracts the <code  data-code-language="text">startTimestamp</code> from the <code  data-code-language="text">endTimestamp</code> and returns the difference in multiples of <code  data-code-language="text">unit</code>.
If <code  data-code-language="text">startTimestamp</code> is later than the <code  data-code-language="text">endTimestamp</code>, negative values are returned.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW date1 = TO_DATETIME("2023-12-02T11:00:00.000Z"), date2 = TO_DATETIME("2023-12-02T11:00:00.001Z")
| EVAL dd_ms = DATE_DIFF("microseconds", date1, date2)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>DATE_EXTRACT</h3>
<p>  Extracts parts of a date, like year, month, day, hour.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW date = DATE_PARSE("yyyy-MM-dd", "2022-05-06")
| EVAL year = DATE_EXTRACT("year", date)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>DATE_FORMAT</h3>
<p>  Returns a string representation of a date, in the provided format.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, hire_date
| EVAL hired = DATE_FORMAT("yyyy-MM-dd", hire_date)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>DATE_PARSE</h3>
<p>  Returns a date by parsing the second argument using the format specified in the first argument.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW date_string = "2022-05-06"
| EVAL date = DATE_PARSE("yyyy-MM-dd", date_string)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>DATE_TRUNC</h3>
<p>  Rounds down a date to the closest interval.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, hire_date
| EVAL year_hired = DATE_TRUNC(1 year, hire_date)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>E</h3>
<p>  Returns Euler's number.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW E()
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ENDS_WITH</h3>
<p>  Returns a boolean that indicates whether a keyword string ends with another string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP last_name
| EVAL ln_E = ENDS_WITH(last_name, "d")
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>EXP</h3>
<p>  Returns the value of e raised to the power of the given number.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW d = 5.0
| EVAL s = EXP(d)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>FLOOR</h3>
<p>  Round a number down to the nearest integer.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=1.8
| EVAL a=FLOOR(a)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: This is a noop for <code  data-code-language="text">long</code> (including unsigned) and <code  data-code-language="text">integer</code>.
For <code  data-code-language="text">double</code> this picks the closest <code  data-code-language="text">double</code> value to the integer
similar to Math.floor.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>FROM_BASE64</h3>
<p>  Decode a base64 string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">row a = "ZWxhc3RpYw==" 
| eval d = from_base64(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>GREATEST</h3>
<p>  Returns the maximum value from multiple columns. This is similar to <code  data-code-language="text">MV_MAX</code>
except it is intended to run on multiple columns at once.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = 10, b = 20
| EVAL g = GREATEST(a, b)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: When run on <code  data-code-language="text">keyword</code> or <code  data-code-language="text">text</code> fields, this returns the last string in alphabetical order. When run on <code  data-code-language="text">boolean</code> columns this will return <code  data-code-language="text">true</code> if any values are <code  data-code-language="text">true</code>.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>HASH</h3>
<p>  Computes the hash of the input using various algorithms such as MD5, SHA, SHA-224, SHA-256, SHA-384, SHA-512.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM sample_data 
| WHERE message != "Connection error"
| EVAL md5 = hash("md5", message), sha256 = hash("sha256", message) 
| KEEP message, md5, sha256;
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>HYPOT</h3>
<p>  Returns the hypotenuse of two numbers. The input can be any numeric values, the return value is always a double.
Hypotenuses of infinities are null.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = 3.0, b = 4.0
| EVAL c = HYPOT(a, b)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>IP_PREFIX</h3>
<p>  Truncates an IP to a given prefix length.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">row ip4 = to_ip("*******"), ip6 = to_ip("fe80::cae2:65ff:fece:feb9")
| eval ip4_prefix = ip_prefix(ip4, 24, 0), ip6_prefix = ip_prefix(ip6, 0, 112);
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><span ><span tabindex="0" >Technical Preview</span></span><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>KQL</h3>
<p>  Performs a KQL query. Returns true if the provided KQL query string matches the row.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM books 
| WHERE KQL("author: Faulkner")
| KEEP book_no, author 
| SORT book_no 
| LIMIT 5;
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>LEAST</h3>
<p>  Returns the minimum value from multiple columns. This is similar to <code  data-code-language="text">MV_MIN</code> except it is intended to run on multiple columns at once.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = 10, b = 20
| EVAL l = LEAST(a, b)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>LEFT</h3>
<p>  Returns the substring that extracts 'length' chars from 'string' starting from the left.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP last_name
| EVAL left = LEFT(last_name, 3)
| SORT last_name ASC
| LIMIT 5
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>LENGTH</h3>
<p>  Returns the character length of a string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airports
| WHERE country == "India"
| KEEP city
| EVAL fn_length = LENGTH(city)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: All strings are in UTF-8, so a single character can use multiple bytes.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>LOCATE</h3>
<p>  Returns an integer that indicates the position of a keyword substring within another string.
Returns <code  data-code-language="text">0</code> if the substring cannot be found.
Note that string positions start from <code  data-code-language="text">1</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">row a = "hello"
| eval a_ll = locate(a, "ll")
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>LOG</h3>
<p>  Returns the logarithm of a value to a base. The input can be any numeric value, the return value is always a double.</p>
<p>  Logs of zero, negative numbers, and base of one return <code  data-code-language="text">null</code> as well as a warning.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW base = 2.0, value = 8.0
| EVAL s = LOG(base, value)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>LOG10</h3>
<p>  Returns the logarithm of a value to base 10. The input can be any numeric value, the return value is always a double.</p>
<p>  Logs of 0 and negative numbers return <code  data-code-language="text">null</code> as well as a warning.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW d = 1000.0 
| EVAL s = LOG10(d)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>LTRIM</h3>
<p>  Removes leading whitespaces from a string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW message = "   some text  ",  color = " red "
| EVAL message = LTRIM(message)
| EVAL color = LTRIM(color)
| EVAL message = CONCAT("'", message, "'")
| EVAL color = CONCAT("'", color, "'")
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><span ><span tabindex="0" >Technical Preview</span></span><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MATCH</h3>
<p>  Use <code  data-code-language="text">MATCH</code> to perform a match query on the specified field.
Using <code  data-code-language="text">MATCH</code> is equivalent to using the <code  data-code-language="text">match</code> query in the Elasticsearch Query DSL.</p>
<p>  Match can be used on fields from the text family like  text and  semantic_text,
as well as other field types like keyword, boolean, dates, and numeric types.</p>
<p>  Match can use function named parameters to specify additional options for the match query.
All match query parameters are supported.</p>
<p>  For a simplified syntax, you can use the match operator <code  data-code-language="text">:</code> operator instead of <code  data-code-language="text">MATCH</code>.</p>
<p>  <code  data-code-language="text">MATCH</code> returns true if the provided query matches the row.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM books 
| WHERE MATCH(author, "Faulkner")
| KEEP book_no, author 
| SORT book_no 
| LIMIT 5;
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MD5</h3>
<p>  Computes the MD5 hash of the input.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM sample_data 
| WHERE message != "Connection error"
| EVAL md5 = md5(message)
| KEEP message, md5;
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_APPEND</h3>
<p>  Concatenates values of two multi-value fields.</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_AVG</h3>
<p>  Converts a multivalued field into a single valued field containing the average of all of the values.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=[3, 5, 1, 6]
| EVAL avg_a = MV_AVG(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_CONCAT</h3>
<p>  Converts a multivalued string expression into a single valued column containing the concatenation of all values separated by a delimiter.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=["foo", "zoo", "bar"]
| EVAL j = MV_CONCAT(a, ", ")
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_COUNT</h3>
<p>  Converts a multivalued expression into a single valued column containing a count of the number of values.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=["foo", "zoo", "bar"]
| EVAL count_a = MV_COUNT(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_DEDUPE</h3>
<p>  Remove duplicate values from a multivalued field.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=["foo", "foo", "bar", "foo"]
| EVAL dedupe_a = MV_DEDUPE(a)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: <code  data-code-language="text">MV_DEDUPE</code> may, but won't always, sort the values in the column.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_FIRST</h3>
<p>  Converts a multivalued expression into a single valued column containing the
first value. This is most useful when reading from a function that emits
multivalued columns in a known order like <code  data-code-language="text">SPLIT</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a="foo;bar;baz"
| EVAL first_a = MV_FIRST(SPLIT(a, ";"))
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_LAST</h3>
<p>  Converts a multivalue expression into a single valued column containing the last
value. This is most useful when reading from a function that emits multivalued
columns in a known order like <code  data-code-language="text">SPLIT</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a="foo;bar;baz"
| EVAL last_a = MV_LAST(SPLIT(a, ";"))
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_MAX</h3>
<p>  Converts a multivalued expression into a single valued column containing the maximum value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=[3, 5, 1]
| EVAL max_a = MV_MAX(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_MEDIAN</h3>
<p>  Converts a multivalued field into a single valued field containing the median value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=[3, 5, 1]
| EVAL median_a = MV_MEDIAN(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_MEDIAN_ABSOLUTE_DEVIATION</h3>
<p>  Converts a multivalued field into a single valued field containing the median absolute deviation.</p>
<p>  It is calculated as the median of each data point's deviation from the median of the entire sample. That is, for a random variable <code  data-code-language="text">X</code>, the median absolute deviation is <code  data-code-language="text">median(|median(X) - X|)</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW values = [0, 2, 5, 6]
| EVAL median_absolute_deviation = MV_MEDIAN_ABSOLUTE_DEVIATION(values), median = MV_MEDIAN(values)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: If the field has an even number of values, the medians will be calculated as the average of the middle two values. If the value is not a floating point number, the averages are rounded towards 0.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_MIN</h3>
<p>  Converts a multivalued expression into a single valued column containing the minimum value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=[2, 1]
| EVAL min_a = MV_MIN(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_PERCENTILE</h3>
<p>  Converts a multivalued field into a single valued field containing the value at which a certain percentage of observed values occur.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW values = [5, 5, 10, 12, 5000]
| EVAL p50 = MV_PERCENTILE(values, 50), median = MV_MEDIAN(values)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_PSERIES_WEIGHTED_SUM</h3>
<p>  Converts a multivalued expression into a single-valued column by multiplying every element on the input list by its corresponding term in P-Series and computing the sum.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = [70.0, 45.0, 21.0, 21.0, 21.0]
| EVAL sum = MV_PSERIES_WEIGHTED_SUM(a, 1.5)
| KEEP sum
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_SLICE</h3>
<p>  Returns a subset of the multivalued field using the start and end index values.
This is most useful when reading from a function that emits multivalued columns
in a known order like <code  data-code-language="text">SPLIT</code> or <code  data-code-language="text">MV_SORT</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">row a = [1, 2, 2, 3]
| eval a1 = mv_slice(a, 1), a2 = mv_slice(a, 2, 3)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_SORT</h3>
<p>  Sorts a multivalued field in lexicographical order.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = [4, 2, -3, 2]
| EVAL sa = mv_sort(a), sd = mv_sort(a, "DESC")
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_SUM</h3>
<p>  Converts a multivalued field into a single valued field containing the sum of all of the values.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=[3, 5, 6]
| EVAL sum_a = MV_SUM(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MV_ZIP</h3>
<p>  Combines the values from two multivalued fields with a delimiter that joins them together.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = ["x", "y", "z"], b = ["1", "2"]
| EVAL c = mv_zip(a, b, "-")
| KEEP a, b, c
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>NOW</h3>
<p>  Returns current date and time.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW current_date = NOW()
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>PI</h3>
<p>  Returns Pi, the ratio of a circle's circumference to its diameter.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW PI()
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>POW</h3>
<p>  Returns the value of <code  data-code-language="text">base</code> raised to the power of <code  data-code-language="text">exponent</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW base = 2.0, exponent = 2
| EVAL result = POW(base, exponent)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: It is still possible to overflow a double result here; in that case, null will be returned.
</p></div></article><article ><span ><span tabindex="0" >Technical Preview</span></span><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>QSTR</h3>
<p>  Performs a query string query. Returns true if the provided query string matches the row.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM books 
| WHERE QSTR("author: Faulkner")
| KEEP book_no, author 
| SORT book_no 
| LIMIT 5;
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>REPEAT</h3>
<p>  Returns a string constructed by concatenating <code  data-code-language="text">string</code> with itself the specified <code  data-code-language="text">number</code> of times.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = "Hello!"
| EVAL triple_a = REPEAT(a, 3)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>REPLACE</h3>
<p>  The function substitutes in the string <code  data-code-language="text">str</code> any match of the regular expression <code  data-code-language="text">regex</code>
with the replacement string <code  data-code-language="text">newStr</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW str = "Hello World"
| EVAL str = REPLACE(str, "World", "Universe")
| KEEP str
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>REVERSE</h3>
<p>  Returns a new string representing the input string in reverse order.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW message = "Some Text" | EVAL message_reversed = REVERSE(message);
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>RIGHT</h3>
<p>  Return the substring that extracts 'length' chars from 'str' starting from the right.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP last_name
| EVAL right = RIGHT(last_name, 3)
| SORT last_name ASC
| LIMIT 5
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ROUND</h3>
<p>  Rounds a number to the specified number of decimal places.
Defaults to 0, which returns the nearest integer. If the
precision is a negative number, rounds to the number of digits left
of the decimal point.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP first_name, last_name, height
| EVAL height_ft = ROUND(height * 3.281, 1)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>RTRIM</h3>
<p>  Removes trailing whitespaces from a string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW message = "   some text  ",  color = " red "
| EVAL message = RTRIM(message)
| EVAL color = RTRIM(color)
| EVAL message = CONCAT("'", message, "'")
| EVAL color = CONCAT("'", color, "'")
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>SHA1</h3>
<p>  Computes the SHA1 hash of the input.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM sample_data 
| WHERE message != "Connection error"
| EVAL sha1 = sha1(message)
| KEEP message, sha1;
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>SHA256</h3>
<p>  Computes the SHA256 hash of the input.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM sample_data 
| WHERE message != "Connection error"
| EVAL sha256 = sha256(message)
| KEEP message, sha256;
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>SIGNUM</h3>
<p>  Returns the sign of the given number.
It returns <code  data-code-language="text">-1</code> for negative numbers, <code  data-code-language="text">0</code> for <code  data-code-language="text">0</code> and <code  data-code-language="text">1</code> for positive numbers.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW d = 100.0
| EVAL s = SIGNUM(d)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>SIN</h3>
<p>  Returns the sine of an angle.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=1.8 
| EVAL sin=SIN(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>SINH</h3>
<p>  Returns the hyperbolic sine of a number.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=1.8 
| EVAL sinh=SINH(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>SPACE</h3>
<p>  Returns a string made of <code  data-code-language="text">number</code> spaces.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW message = CONCAT("Hello", SPACE(1), "World!");
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>SPLIT</h3>
<p>  Split a single valued string into multiple strings.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW words="foo;bar;baz;qux;quux;corge"
| EVAL word = SPLIT(words, ";")
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>SQRT</h3>
<p>  Returns the square root of a number. The input can be any numeric value, the return value is always a double.
Square roots of negative numbers and infinities are null.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW d = 100.0
| EVAL s = SQRT(d)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_CONTAINS</h3>
<p>  Returns whether the first geometry contains the second geometry.
This is the inverse of the <code  data-code-language="text">ST_WITHIN</code> function.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airport_city_boundaries
| WHERE ST_CONTAINS(city_boundary, TO_GEOSHAPE("POLYGON((109.35 18.3, 109.45 18.3, 109.45 18.4, 109.35 18.4, 109.35 18.3))"))
| KEEP abbrev, airport, region, city, city_location
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_DISJOINT</h3>
<p>  Returns whether the two geometries or geometry columns are disjoint.
This is the inverse of the <code  data-code-language="text">ST_INTERSECTS</code> function.
In mathematical terms: ST_Disjoint(A, B) ⇔ A ⋂ B = ∅</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airport_city_boundaries
| WHERE ST_DISJOINT(city_boundary, TO_GEOSHAPE("POLYGON((-10 -60, 120 -60, 120 60, -10 60, -10 -60))"))
| KEEP abbrev, airport, region, city, city_location
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_DISTANCE</h3>
<p>  Computes the distance between two points.
For cartesian geometries, this is the pythagorean distance in the same units as the original coordinates.
For geographic geometries, this is the circular distance along the great circle in meters.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airports
| WHERE abbrev == "CPH"
| EVAL distance = ST_DISTANCE(location, city_location)
| KEEP abbrev, name, location, city_location, distance
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_ENVELOPE</h3>
<p>  Determines the minimum bounding box of the supplied geometry.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airport_city_boundaries
| WHERE abbrev == "CPH"
| EVAL envelope = ST_ENVELOPE(city_boundary)
| KEEP abbrev, airport, envelope
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_INTERSECTS</h3>
<p>  Returns true if two geometries intersect.
They intersect if they have any point in common, including their interior points
(points along lines or within polygons).
This is the inverse of the <code  data-code-language="text">ST_DISJOINT</code> function.
In mathematical terms: ST_Intersects(A, B) ⇔ A ⋂ B ≠ ∅</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airports
| WHERE ST_INTERSECTS(location, TO_GEOSHAPE("POLYGON((42 14, 43 14, 43 15, 42 15, 42 14))"))
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_WITHIN</h3>
<p>  Returns whether the first geometry is within the second geometry.
This is the inverse of the <code  data-code-language="text">ST_CONTAINS</code> function.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airport_city_boundaries
| WHERE ST_WITHIN(city_boundary, TO_GEOSHAPE("POLYGON((109.1 18.15, 109.6 18.15, 109.6 18.65, 109.1 18.65, 109.1 18.15))"))
| KEEP abbrev, airport, region, city, city_location
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_X</h3>
<p>  Extracts the <code  data-code-language="text">x</code> coordinate from the supplied point.
If the points is of type <code  data-code-language="text">geo_point</code> this is equivalent to extracting the <code  data-code-language="text">longitude</code> value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW point = TO_GEOPOINT("POINT(42.97109629958868 14.7552534006536)")
| EVAL x =  ST_X(point), y = ST_Y(point)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_XMAX</h3>
<p>  Extracts the maximum value of the <code  data-code-language="text">x</code> coordinates from the supplied geometry.
If the geometry is of type <code  data-code-language="text">geo_point</code> or <code  data-code-language="text">geo_shape</code> this is equivalent to extracting the maximum <code  data-code-language="text">longitude</code> value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airport_city_boundaries
| WHERE abbrev == "CPH"
| EVAL envelope = ST_ENVELOPE(city_boundary)
| EVAL xmin = ST_XMIN(envelope), xmax = ST_XMAX(envelope), ymin = ST_YMIN(envelope), ymax = ST_YMAX(envelope)
| KEEP abbrev, airport, xmin, xmax, ymin, ymax
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_XMIN</h3>
<p>  Extracts the minimum value of the <code  data-code-language="text">x</code> coordinates from the supplied geometry.
If the geometry is of type <code  data-code-language="text">geo_point</code> or <code  data-code-language="text">geo_shape</code> this is equivalent to extracting the minimum <code  data-code-language="text">longitude</code> value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airport_city_boundaries
| WHERE abbrev == "CPH"
| EVAL envelope = ST_ENVELOPE(city_boundary)
| EVAL xmin = ST_XMIN(envelope), xmax = ST_XMAX(envelope), ymin = ST_YMIN(envelope), ymax = ST_YMAX(envelope)
| KEEP abbrev, airport, xmin, xmax, ymin, ymax
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_Y</h3>
<p>  Extracts the <code  data-code-language="text">y</code> coordinate from the supplied point.
If the points is of type <code  data-code-language="text">geo_point</code> this is equivalent to extracting the <code  data-code-language="text">latitude</code> value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW point = TO_GEOPOINT("POINT(42.97109629958868 14.7552534006536)")
| EVAL x =  ST_X(point), y = ST_Y(point)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_YMAX</h3>
<p>  Extracts the maximum value of the <code  data-code-language="text">y</code> coordinates from the supplied geometry.
If the geometry is of type <code  data-code-language="text">geo_point</code> or <code  data-code-language="text">geo_shape</code> this is equivalent to extracting the maximum <code  data-code-language="text">latitude</code> value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airport_city_boundaries
| WHERE abbrev == "CPH"
| EVAL envelope = ST_ENVELOPE(city_boundary)
| EVAL xmin = ST_XMIN(envelope), xmax = ST_XMAX(envelope), ymin = ST_YMIN(envelope), ymax = ST_YMAX(envelope)
| KEEP abbrev, airport, xmin, xmax, ymin, ymax
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_YMIN</h3>
<p>  Extracts the minimum value of the <code  data-code-language="text">y</code> coordinates from the supplied geometry.
If the geometry is of type <code  data-code-language="text">geo_point</code> or <code  data-code-language="text">geo_shape</code> this is equivalent to extracting the minimum <code  data-code-language="text">latitude</code> value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airport_city_boundaries
| WHERE abbrev == "CPH"
| EVAL envelope = ST_ENVELOPE(city_boundary)
| EVAL xmin = ST_XMIN(envelope), xmax = ST_XMAX(envelope), ymin = ST_YMIN(envelope), ymax = ST_YMAX(envelope)
| KEEP abbrev, airport, xmin, xmax, ymin, ymax
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>STARTS_WITH</h3>
<p>  Returns a boolean that indicates whether a keyword string starts with another string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP last_name
| EVAL ln_S = STARTS_WITH(last_name, "B")
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>SUBSTRING</h3>
<p>  Returns a substring of a string, specified by a start position and an optional length.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| KEEP last_name
| EVAL ln_sub = SUBSTRING(last_name, 1, 3)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TAN</h3>
<p>  Returns the tangent of an angle.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=1.8 
| EVAL tan=TAN(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TANH</h3>
<p>  Returns the hyperbolic tangent of a number.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=1.8 
| EVAL tanh=TANH(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TAU</h3>
<p>  Returns the ratio of a circle's circumference to its radius.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW TAU()
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_BASE64</h3>
<p>  Encode a string to a base64 string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">row a = "elastic" 
| eval e = to_base64(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_BOOLEAN</h3>
<p>  Converts an input value to a boolean value.
A string value of <em>true</em> will be case-insensitive converted to the Boolean <em>true</em>.
For anything else, including the empty string, the function will return <em>false</em>.
The numerical value of <em>0</em> will be converted to <em>false</em>, anything else will be converted to <em>true</em>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW str = ["true", "TRuE", "false", "", "yes", "1"]
| EVAL bool = TO_BOOLEAN(str)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_CARTESIANPOINT</h3>
<p>  Converts an input value to a <code  data-code-language="text">cartesian_point</code> value.
A string will only be successfully converted if it respects WKT Point format.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW wkt = ["POINT(4297.11 -1475.53)", "POINT(7580.93 2272.77)"]
| MV_EXPAND wkt
| EVAL pt = TO_CARTESIANPOINT(wkt)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_CARTESIANSHAPE</h3>
<p>  Converts an input value to a <code  data-code-language="text">cartesian_shape</code> value.
A string will only be successfully converted if it respects WKT format.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW wkt = ["POINT(4297.11 -1475.53)", "POLYGON ((3339584.72 1118889.97, 4452779.63 4865942.27, 2226389.81 4865942.27, 1113194.90 2273030.92, 3339584.72 1118889.97))"]
| MV_EXPAND wkt
| EVAL geom = TO_CARTESIANSHAPE(wkt)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_DATE_NANOS</h3>
<p>  Converts an input to a nanosecond-resolution date value (aka date_nanos).</p>
<p>  Note: The range for date nanos is 1970-01-01T00:00:00.000000000Z to 2262-04-11T23:47:16.854775807Z, attepting to convertvalues outside of that range will result in null with a warning..  Additionally, integers cannot be converted into date nanos, as the range of integer nanoseconds only covers about 2 seconds after epoch.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_DATEPERIOD</h3>
<p>  Converts an input value into a <code  data-code-language="text">date_period</code> value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">row x = "2024-01-01"::datetime | eval y = x + "3 DAYS"::date_period, z = x - to_dateperiod("3 days");
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_DATETIME</h3>
<p>  Converts an input value to a date value.
A string will only be successfully converted if it's respecting the format <code  data-code-language="text">yyyy-MM-dd'T'HH:mm:ss.SSS'Z'</code>.
To convert dates in other formats, use <code  data-code-language="text">DATE_PARSE</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW string = ["1953-09-02T00:00:00.000Z", "1964-06-02T00:00:00.000Z", "1964-06-02 00:00:00"]
| EVAL datetime = TO_DATETIME(string)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: Note that when converting from nanosecond resolution to millisecond resolution with this function, the nanosecond date is truncated, not rounded.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_DEGREES</h3>
<p>  Converts a number in radians to degrees.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW rad = [1.57, 3.14, 4.71]
| EVAL deg = TO_DEGREES(rad)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_DOUBLE</h3>
<p>  Converts an input value to a double value. If the input parameter is of a date type,
its value will be interpreted as milliseconds since the Unix epoch,
converted to double. Boolean <em>true</em> will be converted to double <em>1.0</em>, <em>false</em> to <em>0.0</em>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW str1 = "5.20128E11", str2 = "foo"
| EVAL dbl = TO_DOUBLE("520128000000"), dbl1 = TO_DOUBLE(str1), dbl2 = TO_DOUBLE(str2)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_GEOPOINT</h3>
<p>  Converts an input value to a <code  data-code-language="text">geo_point</code> value.
A string will only be successfully converted if it respects WKT Point format.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW wkt = "POINT(42.97109630194 14.7552534413725)"
| EVAL pt = TO_GEOPOINT(wkt)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_GEOSHAPE</h3>
<p>  Converts an input value to a <code  data-code-language="text">geo_shape</code> value.
A string will only be successfully converted if it respects WKT format.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW wkt = "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10))"
| EVAL geom = TO_GEOSHAPE(wkt)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_INTEGER</h3>
<p>  Converts an input value to an integer value.
If the input parameter is of a date type, its value will be interpreted as milliseconds
since the Unix epoch, converted to integer.
Boolean <em>true</em> will be converted to integer <em>1</em>, <em>false</em> to <em>0</em>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW long = [5013792, 2147483647, 501379200000]
| EVAL int = TO_INTEGER(long)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_IP</h3>
<p>  Converts an input string to an IP value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW str1 = "*******", str2 = "foo"
| EVAL ip1 = TO_IP(str1), ip2 = TO_IP(str2)
| WHERE CIDR_MATCH(ip1, "*******/8")
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_LONG</h3>
<p>  Converts an input value to a long value. If the input parameter is of a date type,
its value will be interpreted as milliseconds since the Unix epoch, converted to long.
Boolean <em>true</em> will be converted to long <em>1</em>, <em>false</em> to <em>0</em>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW str1 = "2147483648", str2 = "2147483648.2", str3 = "foo"
| EVAL long1 = TO_LONG(str1), long2 = TO_LONG(str2), long3 = TO_LONG(str3)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_LOWER</h3>
<p>  Returns a new string representing the input string converted to lower case.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW message = "Some Text"
| EVAL message_lower = TO_LOWER(message)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_RADIANS</h3>
<p>  Converts a number in degrees to radians.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW deg = [90.0, 180.0, 270.0]
| EVAL rad = TO_RADIANS(deg)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_STRING</h3>
<p>  Converts an input value into a string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a=10
| EVAL j = TO_STRING(a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_TIMEDURATION</h3>
<p>  Converts an input value into a <code  data-code-language="text">time_duration</code> value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">row x = "2024-01-01"::datetime | eval y = x + "3 hours"::time_duration, z = x - to_timeduration("3 hours");
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_UNSIGNED_LONG</h3>
<p>  Converts an input value to an unsigned long value. If the input parameter is of a date type,
its value will be interpreted as milliseconds since the Unix epoch, converted to unsigned long.
Boolean <em>true</em> will be converted to unsigned long <em>1</em>, <em>false</em> to <em>0</em>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW str1 = "2147483648", str2 = "2147483648.2", str3 = "foo"
| EVAL long1 = TO_UNSIGNED_LONG(str1), long2 = TO_ULONG(str2), long3 = TO_UL(str3)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_UPPER</h3>
<p>  Returns a new string representing the input string converted to upper case.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW message = "Some Text"
| EVAL message_upper = TO_UPPER(message)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TO_VERSION</h3>
<p>  Converts an input string to a version value.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW v = TO_VERSION("1.2.3")
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TRIM</h3>
<p>  Removes leading and trailing whitespaces from a string.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW message = "   some text  ",  color = " red "
| EVAL message = TRIM(message)
| EVAL color = TRIM(color)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article></section><section class="css-fn8403"><h2>Aggregation functions</h2><p>These functions can by used with STATS...BY:</p><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>AVG</h3>
<p>  The average of a numeric field.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS AVG(height)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>COUNT</h3>
<p>  Returns the total number (count) of input values.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS COUNT(height)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>COUNT_DISTINCT</h3>
<p>  Returns the approximate number of distinct values.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM hosts
| STATS COUNT_DISTINCT(ip0), COUNT_DISTINCT(ip1)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MAX</h3>
<p>  The maximum value of a field.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS MAX(languages)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MEDIAN</h3>
<p>  The value that is greater than half of all values and less than half of all values, also known as the 50% <code  data-code-language="text">PERCENTILE</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS MEDIAN(salary), PERCENTILE(salary, 50)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: Like <code  data-code-language="text">PERCENTILE</code>, <code  data-code-language="text">MEDIAN</code> is usually approximate.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MEDIAN_ABSOLUTE_DEVIATION</h3>
<p>  Returns the median absolute deviation, a measure of variability. It is a robust statistic, meaning that it is useful for describing data that may have outliers, or may not be normally distributed. For such data it can be more descriptive than standard deviation.</p>
<p>  It is calculated as the median of each data point's deviation from the median of the entire sample. That is, for a random variable <code  data-code-language="text">X</code>, the median absolute deviation is <code  data-code-language="text">median(|median(X) - X|)</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS MEDIAN(salary), MEDIAN_ABSOLUTE_DEVIATION(salary)
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>  Note: Like <code  data-code-language="text">PERCENTILE</code>, <code  data-code-language="text">MEDIAN_ABSOLUTE_DEVIATION</code> is usually approximate.
</p></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>MIN</h3>
<p>  The minimum value of a field.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS MIN(languages)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>PERCENTILE</h3>
<p>  Returns the value at which a certain percentage of observed values occur. For example, the 95th percentile is the value which is greater than 95% of the observed values and the 50th percentile is the <code  data-code-language="text">MEDIAN</code>.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS p0 = PERCENTILE(salary,  0)
     , p50 = PERCENTILE(salary, 50)
     , p99 = PERCENTILE(salary, 99)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_CENTROID_AGG</h3>
<p>  Calculate the spatial centroid over a field with spatial point geometry type.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airports
| STATS centroid=ST_CENTROID_AGG(location)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>ST_EXTENT_AGG</h3>
<p>  Calculate the spatial extent over a field with geometry type. Returns a bounding box for all values of the field.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM airports
| WHERE country == "India"
| STATS extent = ST_EXTENT_AGG(location)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>STD_DEV</h3>
<p>  The standard deviation of a numeric field.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS STD_DEV(height)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>SUM</h3>
<p>  The sum of a numeric expression.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS SUM(languages)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>TOP</h3>
<p>  Collects the top values for a field. Includes repeated values.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS top_salaries = TOP(salary, 3, "desc"), top_salary = MAX(salary)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><span ><span tabindex="0" >Technical Preview</span></span><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>VALUES</h3>
<p>  Returns all values in a group as a multivalued field. The order of the returned values isn't guaranteed. If you need the values returned in order use esql-mv_sort.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">  FROM employees
| EVAL first_letter = SUBSTRING(first_name, 0, 1)
| STATS first_name=MV_SORT(VALUES(first_name)) BY first_letter
| SORT first_letter
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component">
<h3>WEIGHTED_AVG</h3>
<p>  The weighted average of a numeric expression.</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS w_avg = WEIGHTED_AVG(salary, height) by languages
| EVAL w_avg = ROUND(w_avg)
| KEEP w_avg, languages
| SORT languages
</code></pre><div ><div ><span ></span></div></div></div></div></div></article></section><section class="css-fn8403"><h2>Grouping functions</h2><p>These grouping functions can be used with `STATS...BY`:</p><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>BUCKET</h3>
<p>Creates groups of values - buckets - out of a datetime or numeric input. The size of the buckets can either be provided directly, or chosen based on a recommended count and values range.</p>
<p><code  data-code-language="text">BUCKET</code> works in two modes:</p>
<ol>
<li>Where the size of the bucket is computed based on a buckets count recommendation (four parameters) and a range.</li>
<li>Where the bucket size is provided directly (two parameters).</li>
</ol>
<p>Using a target number of buckets, a start of a range, and an end of a range, <code  data-code-language="text">BUCKET</code> picks an appropriate bucket size to generate the target number of buckets or fewer.</p>
<p>For example, requesting up to 20 buckets for a year will organize data into monthly intervals:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE hire_date &gt;= "1985-01-01T00:00:00Z" AND hire_date &lt; "1986-01-01T00:00:00Z"
| STATS hire_date = MV_SORT(VALUES(hire_date)) BY month = BUCKET(hire_date, 20, "1985-01-01T00:00:00Z", "1986-01-01T00:00:00Z")
| SORT hire_date
</code></pre><div ><div ><span ></span></div></div></div></div>
<p><strong>NOTE</strong>: The goal isn’t to provide the exact target number of buckets, it’s to pick a range that provides <em>at most</em> the target number of buckets.</p>
<p>You can combine <code  data-code-language="text">BUCKET</code> with an aggregation to create a histogram:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE hire_date &gt;= "1985-01-01T00:00:00Z" AND hire_date &lt; "1986-01-01T00:00:00Z"
| STATS hires_per_month = COUNT(*) BY month = BUCKET(hire_date, 20, "1985-01-01T00:00:00Z", "1986-01-01T00:00:00Z")
| SORT month
</code></pre><div ><div ><span ></span></div></div></div></div>
<p><strong>NOTE</strong>: <code  data-code-language="text">BUCKET</code> does not create buckets that match zero documents. That’s why the previous example is missing <code  data-code-language="text">1985-03-01</code> and other dates.</p>
<p>Asking for more buckets can result in a smaller range. For example, requesting at most 100 buckets in a year results in weekly buckets:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE hire_date &gt;= "1985-01-01T00:00:00Z" AND hire_date &lt; "1986-01-01T00:00:00Z"
| STATS hires_per_week = COUNT(*) BY week = BUCKET(hire_date, 100, "1985-01-01T00:00:00Z", "1986-01-01T00:00:00Z")
| SORT week
</code></pre><div ><div ><span ></span></div></div></div></div>
<p><strong>NOTE</strong>: <code  data-code-language="text">BUCKET</code> does not filter any rows. It only uses the provided range to pick a good bucket size. For rows with a value outside of the range, it returns a bucket value that corresponds to a bucket outside the range. Combine <code  data-code-language="text">BUCKET</code> with <code  data-code-language="text">WHERE</code> to filter rows.</p>
<p>If the desired bucket size is known in advance, simply provide it as the second argument, leaving the range out:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE hire_date &gt;= "1985-01-01T00:00:00Z" AND hire_date &lt; "1986-01-01T00:00:00Z"
| STATS hires_per_week = COUNT(*) BY week = BUCKET(hire_date, 1 week)
| SORT week
</code></pre><div ><div ><span ></span></div></div></div></div>
<p><strong>NOTE</strong>: When providing the bucket size as the second parameter, it must be a time duration or date period.</p>
<p><code  data-code-language="text">BUCKET</code> can also operate on numeric fields. For example, to create a salary histogram:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS COUNT(*) by bs = BUCKET(salary, 20, 25324, 74999)
| SORT bs
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Unlike the earlier example that intentionally filters on a date range, you rarely want to filter on a numeric range. You have to find the min and max separately. ES|QL doesn’t yet have an easy way to do that automatically.</p>
<p>The range can be omitted if the desired bucket size is known in advance. Simply provide it as the second argument:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE hire_date &gt;= "1985-01-01T00:00:00Z" AND hire_date &lt; "1986-01-01T00:00:00Z"
| STATS c = COUNT(1) BY b = BUCKET(salary, 5000.)
| SORT b
</code></pre><div ><div ><span ></span></div></div></div></div>
<p><strong>NOTE</strong>: When providing the bucket size as the second parameter, it must be of a <strong>floating point type</strong>.</p>
<p>Here's an example to create hourly buckets for the last 24 hours, and calculate the number of events per hour:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM sample_data
| WHERE @timestamp &gt;= NOW() - 1 day and @timestamp &lt; NOW()
| STATS COUNT(*) BY bucket = BUCKET(@timestamp, 25, NOW() - 1 day, NOW())
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Here's an example  to create monthly buckets for the year 1985, and calculate the average salary by hiring month:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE hire_date &gt;= "1985-01-01T00:00:00Z" AND hire_date &lt; "1986-01-01T00:00:00Z"
| STATS AVG(salary) BY bucket = BUCKET(hire_date, 20, "1985-01-01T00:00:00Z", "1986-01-01T00:00:00Z")
| SORT bucket
</code></pre><div ><div ><span ></span></div></div></div></div>
<p><code  data-code-language="text">BUCKET</code> may be used in both the aggregating and grouping part of the <code  data-code-language="text">STATS …​ BY …</code>​ command, provided that in the aggregating part the function is <strong>referenced by an alias defined in the grouping part</strong>, or that it is invoked with the exact same expression.</p>
<p>For example:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| STATS s1 = b1 + 1, s2 = BUCKET(salary / 1000 + 999, 50.) + 2 BY b1 = BUCKET(salary / 100 + 99, 50.), b2 = BUCKET(salary / 1000 + 999, 50.)
| SORT b1, b2
| KEEP s1, b1, s2, b2
</code></pre><div ><div ><span ></span></div></div></div></div></div></article></section><section class="css-fn8403"><h2>Operators</h2><p>ES|QL supports the following operators:</p><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>Binary operators</h3>
<p>These binary comparison operators are supported:</p>
<ul>
<li>equality: <code  data-code-language="text">==</code></li>
<li>inequality: <code  data-code-language="text">!=</code></li>
<li>less than: <code  data-code-language="text">&lt;</code></li>
<li>less than or equal: <code  data-code-language="text">&lt;=</code></li>
<li>greater than: <code  data-code-language="text">&gt;</code></li>
<li>greater than or equal: <code  data-code-language="text">&gt;=</code></li>
<li>add: <code  data-code-language="text">+</code></li>
<li>subtract: <code  data-code-language="text">-</code></li>
<li>multiply: <code  data-code-language="text">*</code></li>
<li>divide: <code  data-code-language="text">/</code></li>
<li>modulus: <code  data-code-language="text">%</code>
</li>
</ul></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>Boolean operators</h3>
<p>The following boolean operators are supported:</p>
<ul>
<li><code  data-code-language="text">AND</code></li>
<li><code  data-code-language="text">OR</code></li>
<li><code  data-code-language="text">NOT</code>
</li>
</ul></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>CAST (<code  data-code-language="text">::</code>)</h3>
<p>The <code  data-code-language="text">::</code> operator provides a convenient alternative syntax to the <code  data-code-language="text">TO_&lt;type&gt;</code> type conversion functions.</p>
<p>Example:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW ver = CONCAT(("0"::INT + 1)::STRING, ".2.3")::VERSION
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>IN</h3>
<p>The <code  data-code-language="text">IN</code> operator allows testing whether a field or expression equals an element in a list of literals, fields or expressions:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">ROW a = 1, b = 4, c = 3
| WHERE c-a IN (3, b / 2, a)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>LIKE and RLIKE</h3>
<p>For string comparison using wildcards or regular expressions, use <code  data-code-language="text">LIKE</code> or <code  data-code-language="text">RLIKE</code>:</p>
<p>Use <code  data-code-language="text">LIKE</code> to match strings using wildcards. The following wildcard characters are supported:</p>
<ul>
<li><code  data-code-language="text">*</code> matches zero or more characters.</li>
<li><code  data-code-language="text">?</code> matches one character.</li>
</ul>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE first_name LIKE "?b*"
| KEEP first_name, last_name
</code></pre><div ><div ><span ></span></div></div></div></div>
<p>Use <code  data-code-language="text">RLIKE</code> to match strings using regular expressions:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE first_name RLIKE ".leja.*"
| KEEP first_name, last_name
</code></pre><div ><div ><span ></span></div></div></div></div></div></article><article ><div class="euiText euiMarkdownFormat css-1v70vnz-euiText-relative-euiTextColor-inherit-euiMarkdownFormat-relative-inherit" aria-label="markdown component"><h3>NULL values</h3>
<p>For NULL comparison use the <code  data-code-language="text">IS NULL</code> and <code  data-code-language="text">IS NOT NULL</code> predicates:</p>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE birth_date IS NULL
| KEEP first_name, last_name
| SORT first_name
| LIMIT 3
</code></pre><div ><div ><span ></span></div></div></div></div>
<div ><div ><pre  tabindex="-1"><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><div >text code block:</div><span  aria-hidden="true" data-tabular-copy-marker="no-copy"></span><code  data-code-language="text">FROM employees
| WHERE is_rehired IS NOT NULL
| STATS count(emp_no)
</code></pre><div ><div ><span ></span></div></div></div></div></div></article></section></div></div></div></div>