<?php

declare(strict_types=1);

namespace EsqlPhp\Tests\Unit\Functions\Math;

use PHPUnit\Framework\TestCase;
use EsqlPhp\QueryBuilder\Functions\Math\Acos;
use EsqlPhp\QueryBuilder\Functions\Math\Asin;
use EsqlPhp\QueryBuilder\Functions\Math\Atan;
use EsqlPhp\QueryBuilder\Functions\Math\Atan2;
use EsqlPhp\QueryBuilder\Functions\Math\Cbrt;
use EsqlPhp\QueryBuilder\Functions\Math\Ceil;
use EsqlPhp\QueryBuilder\Functions\Math\Cos;
use EsqlPhp\QueryBuilder\Functions\Math\Cosh;
use EsqlPhp\QueryBuilder\Functions\Math\E;
use EsqlPhp\QueryBuilder\Functions\Math\Exp;
use EsqlPhp\QueryBuilder\Functions\Math\Expm1;
use EsqlPhp\QueryBuilder\Functions\Math\Floor;
use EsqlPhp\QueryBuilder\Functions\Math\Log;
use EsqlPhp\QueryBuilder\Functions\Math\Log10;
use EsqlPhp\QueryBuilder\Functions\Math\Log1p;
use EsqlPhp\QueryBuilder\Functions\Math\Pi;
use EsqlPhp\QueryBuilder\Functions\Math\Pow;
use EsqlPhp\QueryBuilder\Functions\Math\Radians;
use EsqlPhp\QueryBuilder\Functions\Math\Random;
use EsqlPhp\QueryBuilder\Functions\Math\Round;
use EsqlPhp\QueryBuilder\Functions\Math\Sign;
use EsqlPhp\QueryBuilder\Functions\Math\Sin;
use EsqlPhp\QueryBuilder\Functions\Math\Sinh;
use EsqlPhp\QueryBuilder\Functions\Math\Sqrt;
use EsqlPhp\QueryBuilder\Functions\Math\Tan;
use EsqlPhp\QueryBuilder\Functions\Math\Tanh;
use EsqlPhp\QueryBuilder\Functions\Math\Trunc;

class MathFunctionsTest extends TestCase
{
    public function testAbs(): void
    {
        $f = new \EsqlPhp\QueryBuilder\Functions\Math\Abs(-2);
        $this->assertEquals('abs', $f->getName());
    }

    public function testAcos(): void
    {
        $f = new Acos(1);
        $this->assertEquals('acos', $f->getName());
    }

    public function testAsin(): void
    {
        $f = new Asin(1);
        $this->assertEquals('asin', $f->getName());
    }

    public function testAtan(): void
    {
        $f = new Atan(1);
        $this->assertEquals('atan', $f->getName());
    }

    public function testAtan2(): void
    {
        $f = new Atan2(1, 1);
        $this->assertEquals('atan2', $f->getName());
    }

    public function testCbrt(): void
    {
        $f = new Cbrt(8);
        $this->assertEquals('cbrt', $f->getName());
    }

    public function testCeil(): void
    {
        $f = new Ceil(1.2);
        $this->assertEquals('ceil', $f->getName());
    }

    public function testCos(): void
    {
        $f = new Cos(0);
        $this->assertEquals('cos', $f->getName());
    }

    public function testCosh(): void
    {
        $f = new Cosh(0);
        $this->assertEquals('cosh', $f->getName());
    }

    public function testE(): void
    {
        $f = new E();
        $this->assertEquals('e', $f->getName());
    }

    public function testExp(): void
    {
        $f = new Exp(1);
        $this->assertEquals('exp', $f->getName());
    }

    public function testExpm1(): void
    {
        $f = new Expm1(1);
        $this->assertEquals('expm1', $f->getName());
    }

    public function testFloor(): void
    {
        $f = new Floor(1.9);
        $this->assertEquals('floor', $f->getName());
    }

    public function testLog(): void
    {
        $f = new Log(1);
        $this->assertEquals('log', $f->getName());
    }

    public function testLog10(): void
    {
        $f = new Log10(1);
        $this->assertEquals('log10', $f->getName());
    }

    public function testLog1p(): void
    {
        $f = new Log1p(1);
        $this->assertEquals('log1p', $f->getName());
    }

    public function testPi(): void
    {
        $f = new Pi();
        $this->assertEquals('pi', $f->getName());
    }

    public function testPow(): void
    {
        $f = new Pow(2, 3);
        $this->assertEquals('pow', $f->getName());
    }

    public function testRadians(): void
    {
        $f = new Radians(180);
        $this->assertEquals('radians', $f->getName());
    }

    public function testRandom(): void
    {
        $f = new Random();
        $this->assertEquals('random', $f->getName());
    }

    public function testRound(): void
    {
        $f = new Round(1.5);
        $this->assertEquals('round', $f->getName());
    }

    public function testSign(): void
    {
        $f = new Sign(-1);
        $this->assertEquals('sign', $f->getName());
    }

    public function testSin(): void
    {
        $f = new Sin(0);
        $this->assertEquals('sin', $f->getName());
    }

    public function testSinh(): void
    {
        $f = new Sinh(0);
        $this->assertEquals('sinh', $f->getName());
    }

    public function testSqrt(): void
    {
        $f = new Sqrt(4);
        $this->assertEquals('sqrt', $f->getName());
    }

    public function testTan(): void
    {
        $f = new Tan(0);
        $this->assertEquals('tan', $f->getName());
    }

    public function testTanh(): void
    {
        $f = new Tanh(0);
        $this->assertEquals('tanh', $f->getName());
    }

    public function testTrunc(): void
    {
        $f = new Trunc(1.9);
        $this->assertEquals('trunc', $f->getName());
    }
}
